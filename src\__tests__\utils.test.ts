import {
  formatCurrency,
  formatDate,
  formatDateTime,
  calculateCostPerEspresso,
  generateSlug,
  isValidEmail,
  isValidSwissPostalCode,
  calculateDiscountPercentage,
  calculatePointsEarned,
  determineUserLevel,
  calculateShippingCost,
  generateCouponCode,
  isValidCouponCode,
  calculateVATFromInclusive,
  calculateExclusiveFromInclusive,
  calculateInclusiveFromExclusive,
  formatVATBreakdown,
} from '../lib/utils'

describe('Currency Formatting', () => {
  it('should format CHF currency correctly', () => {
    expect(formatCurrency(12.90)).toContain('CHF')
    expect(formatCurrency(12.90)).toContain('12.90')
    expect(formatCurrency(0)).toContain('0.00')
    expect(formatCurrency(1000.50)).toContain('1')
    expect(formatCurrency(1000.50)).toContain('000.50')
  })
})

describe('Date Formatting', () => {
  it('should format dates in Swiss format (dd/mm/yyyy)', () => {
    const testDate = new Date('2024-01-15')
    expect(formatDate(testDate)).toBe('15/01/2024')
  })

  it('should format datetime with time', () => {
    const testDate = new Date('2024-01-15T14:30:00')
    expect(formatDateTime(testDate)).toContain('15/01/2024')
    expect(formatDateTime(testDate)).toContain('14:30')
  })
})

describe('Cost Per Espresso Calculation', () => {
  it('should calculate cost per espresso for capsules', () => {
    const cost = calculateCostPerEspresso(12.90, 'capsules', 10)
    expect(cost).toBe(1.29)
  })

  it('should calculate cost per espresso for pods', () => {
    const cost = calculateCostPerEspresso(15.90, 'pods', 12)
    expect(cost).toBeCloseTo(1.325, 3)
  })

  it('should calculate cost per espresso for beans (250g pack)', () => {
    const cost = calculateCostPerEspresso(24.90, 'beans', undefined, 250, 7)
    expect(cost).toBeCloseTo(0.6972, 3) // 24.90 / (250/7)
  })

  it('should calculate cost per espresso for ground coffee', () => {
    const cost = calculateCostPerEspresso(18.90, 'ground', undefined, 250, 7)
    expect(cost).toBeCloseTo(0.5292, 3) // 18.90 / (250/7)
  })

  it('should return 0 for invalid inputs', () => {
    expect(calculateCostPerEspresso(12.90, 'capsules')).toBe(0)
    expect(calculateCostPerEspresso(12.90, 'beans')).toBe(0)
  })
})

describe('Slug Generation', () => {
  it('should generate valid slugs', () => {
    expect(generateSlug('Espresso Intenso')).toBe('espresso-intenso')
    expect(generateSlug('Café au Lait')).toBe('caf-au-lait') // Accented chars removed
    expect(generateSlug('Test & Special Characters!')).toBe('test-special-characters')
  })
})

describe('Email Validation', () => {
  it('should validate correct email addresses', () => {
    expect(isValidEmail('<EMAIL>')).toBe(true)
    expect(isValidEmail('<EMAIL>')).toBe(true)
  })

  it('should reject invalid email addresses', () => {
    expect(isValidEmail('invalid-email')).toBe(false)
    expect(isValidEmail('test@')).toBe(false)
    expect(isValidEmail('@example.com')).toBe(false)
  })
})

describe('Swiss Postal Code Validation', () => {
  it('should validate correct Swiss postal codes', () => {
    expect(isValidSwissPostalCode('8001')).toBe(true) // Zurich
    expect(isValidSwissPostalCode('1200')).toBe(true) // Geneva
    expect(isValidSwissPostalCode('3000')).toBe(true) // Bern
  })

  it('should reject invalid Swiss postal codes', () => {
    expect(isValidSwissPostalCode('0123')).toBe(false) // Cannot start with 0
    expect(isValidSwissPostalCode('12345')).toBe(false) // Too long
    expect(isValidSwissPostalCode('123')).toBe(false) // Too short
  })
})

describe('Discount Calculation', () => {
  it('should calculate discount percentage correctly', () => {
    expect(calculateDiscountPercentage(100, 80)).toBe(20)
    expect(calculateDiscountPercentage(14.90, 12.90)).toBe(13) // Rounded
  })

  it('should return 0 for invalid discounts', () => {
    expect(calculateDiscountPercentage(100, 100)).toBe(0)
    expect(calculateDiscountPercentage(100, 120)).toBe(0)
  })
})

describe('Points Calculation', () => {
  it('should calculate points earned correctly', () => {
    expect(calculatePointsEarned(50)).toBe(50) // 1:1 ratio
    expect(calculatePointsEarned(50, 1.5)).toBe(75) // With multiplier
  })
})

describe('User Level Determination', () => {
  const levels = [
    { level: 1, minimum_points: 0 },
    { level: 2, minimum_points: 200 },
    { level: 3, minimum_points: 500 },
    { level: 4, minimum_points: 1000 },
  ]

  it('should determine correct user level', () => {
    expect(determineUserLevel(50, levels)).toBe(1)
    expect(determineUserLevel(250, levels)).toBe(2)
    expect(determineUserLevel(600, levels)).toBe(3)
    expect(determineUserLevel(1500, levels)).toBe(4)
  })
})

describe('Shipping Cost Calculation', () => {
  const shippingRates = [
    { country: 'CH', cost: 4.90, free_shipping_threshold: 90 },
    { country: 'DE', cost: 9.90, free_shipping_threshold: 75 },
    { country: 'FR', cost: 12.90, free_shipping_threshold: 100 },
  ]

  it('should calculate shipping cost correctly', () => {
    expect(calculateShippingCost('CH', 30, shippingRates)).toBe(4.90)
    expect(calculateShippingCost('DE', 40, shippingRates)).toBe(9.90)
  })

  it('should apply free shipping when threshold is met', () => {
    expect(calculateShippingCost('CH', 100, shippingRates)).toBe(0)
    expect(calculateShippingCost('DE', 80, shippingRates)).toBe(0)
  })

  it('should return 0 for unknown countries', () => {
    expect(calculateShippingCost('US', 100, shippingRates)).toBe(0)
  })
})

describe('Coupon Code Generation', () => {
  it('should generate coupon codes of correct length', () => {
    const code = generateCouponCode(8)
    expect(code).toHaveLength(8)
    expect(/^[A-Z0-9]+$/.test(code)).toBe(true)
  })

  it('should generate different codes', () => {
    const code1 = generateCouponCode()
    const code2 = generateCouponCode()
    expect(code1).not.toBe(code2)
  })
})

describe('Coupon Code Validation', () => {
  it('should validate correct coupon codes', () => {
    expect(isValidCouponCode('WELCOME10')).toBe(true)
    expect(isValidCouponCode('SAVE5CHF')).toBe(true)
    expect(isValidCouponCode('ABC123')).toBe(true)
  })

  it('should reject invalid coupon codes', () => {
    expect(isValidCouponCode('abc')).toBe(false) // Too short
    expect(isValidCouponCode('welcome10')).toBe(false) // Lowercase
    expect(isValidCouponCode('SAVE-5')).toBe(false) // Special characters
  })
})

describe('VAT Calculations', () => {
  const vatRate = 0.077 // 7.7% Swiss VAT

  it('should calculate VAT amount from inclusive price', () => {
    expect(calculateVATFromInclusive(100, vatRate)).toBeCloseTo(7.15, 2)
    expect(calculateVATFromInclusive(25, vatRate)).toBeCloseTo(1.79, 2)
    expect(calculateVATFromInclusive(0, vatRate)).toBe(0)
  })

  it('should calculate exclusive price from inclusive price', () => {
    expect(calculateExclusiveFromInclusive(100, vatRate)).toBeCloseTo(92.85, 2)
    expect(calculateExclusiveFromInclusive(25, vatRate)).toBeCloseTo(23.21, 2)
    expect(calculateExclusiveFromInclusive(0, vatRate)).toBe(0)
  })

  it('should calculate inclusive price from exclusive price', () => {
    expect(calculateInclusiveFromExclusive(92.85, vatRate)).toBeCloseTo(100, 2)
    expect(calculateInclusiveFromExclusive(23.21, vatRate)).toBeCloseTo(25, 2)
    expect(calculateInclusiveFromExclusive(0, vatRate)).toBe(0)
  })

  it('should format VAT breakdown correctly', () => {
    const breakdown = formatVATBreakdown(100, vatRate)

    expect(breakdown.inclusive).toBe(100)
    expect(breakdown.exclusive).toBeCloseTo(92.85, 2)
    expect(breakdown.vatAmount).toBeCloseTo(7.15, 2)
    expect(breakdown.vatRate).toBe(vatRate)
  })

  it('should handle edge cases', () => {
    // Zero amounts
    expect(calculateVATFromInclusive(0, vatRate)).toBe(0)
    expect(calculateExclusiveFromInclusive(0, vatRate)).toBe(0)
    expect(calculateInclusiveFromExclusive(0, vatRate)).toBe(0)

    // Zero VAT rate
    expect(calculateVATFromInclusive(100, 0)).toBe(0)
    expect(calculateExclusiveFromInclusive(100, 0)).toBe(100)
    expect(calculateInclusiveFromExclusive(100, 0)).toBe(100)
  })

  it('should maintain mathematical consistency', () => {
    const inclusivePrice = 127.50
    const exclusive = calculateExclusiveFromInclusive(inclusivePrice, vatRate)
    const backToInclusive = calculateInclusiveFromExclusive(exclusive, vatRate)

    expect(backToInclusive).toBeCloseTo(inclusivePrice, 2)
  })
})
