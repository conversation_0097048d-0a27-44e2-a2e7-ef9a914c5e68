import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { LevelManagementConfig } from '../level-config/route'
import { generateLevels, calculateLevelStats, validateLevelConfig } from '@/lib/level-generation'



// POST - Generate level preview based on configuration
export async function POST(request: NextRequest) {
  try {
    const config: LevelManagementConfig = await request.json()
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 403 })
    }

    // Validation
    const validationErrors = validateLevelConfig(config)
    if (validationErrors.length > 0) {
      return NextResponse.json({ error: validationErrors.join(', ') }, { status: 400 })
    }

    // Generate preview levels
    const previewLevels = generateLevels(config)

    // Calculate statistics
    const stats = calculateLevelStats(previewLevels)

    return NextResponse.json({ 
      success: true, 
      levels: previewLevels,
      stats
    })
  } catch (error) {
    console.error('Error in level preview:', error)
    return NextResponse.json({ error: 'Interner Serverfehler' }, { status: 500 })
  }
}
