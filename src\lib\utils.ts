import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { format } from "date-fns";
import { de } from "date-fns/locale";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Currency formatting for CHF
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('de-CH', {
    style: 'currency',
    currency: 'CHF',
    minimumFractionDigits: 2,
  }).format(amount);
}

// Date formatting for Swiss locale (dd/mm/yyyy)
export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'dd/MM/yyyy', { locale: de });
}

export function formatDateTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: de });
}

// Calculate cost per espresso for coffee products
export function calculateCostPerEspresso(
  price: number,
  coffeeType: 'capsules' | 'pods' | 'beans' | 'ground',
  packQuantity?: number,
  packWeightGrams?: number,
  gramsPerEspresso: number = 7
): number {
  if (coffeeType === 'capsules' || coffeeType === 'pods') {
    if (!packQuantity) return 0;
    return price / packQuantity;
  }
  
  if (coffeeType === 'beans' || coffeeType === 'ground') {
    if (!packWeightGrams) return 0;
    const espressosPerPack = packWeightGrams / gramsPerEspresso;
    return price / espressosPerPack;
  }
  
  return 0;
}

// Generate slug from string
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Validate email
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate Swiss postal code
export function isValidSwissPostalCode(postalCode: string): boolean {
  const swissPostalRegex = /^[1-9]\d{3}$/;
  return swissPostalRegex.test(postalCode);
}

// Calculate discount percentage
export function calculateDiscountPercentage(originalPrice: number, discountPrice: number): number {
  if (originalPrice <= 0 || discountPrice >= originalPrice) return 0;
  return Math.round(((originalPrice - discountPrice) / originalPrice) * 100);
}

// Calculate points earned based on amount and level multiplier
export function calculatePointsEarned(amount: number, pointsMultiplier: number = 1): number {
  // Base: 1 CHF = 1 point
  return Math.floor(amount * pointsMultiplier);
}

// Determine user level based on total points
export function determineUserLevel(totalPoints: number, levels: Array<{ level: number; minimum_points: number }>): number {
  const sortedLevels = levels.sort((a, b) => b.minimum_points - a.minimum_points);

  for (const levelData of sortedLevels) {
    if (totalPoints >= levelData.minimum_points) {
      return levelData.level;
    }
  }

  return 1; // Default to level 1
}

// Calculate shipping cost
export function calculateShippingCost(
  country: string,
  orderTotal: number,
  shippingRates: Array<{ country: string; cost: number; free_shipping_threshold?: number }>
): number {
  const rate = shippingRates.find(r => r.country === country);
  if (!rate) return 0;
  
  if (rate.free_shipping_threshold && orderTotal >= rate.free_shipping_threshold) {
    return 0;
  }
  
  return rate.cost;
}

// VAT calculation utilities
export function calculateVATFromInclusive(inclusivePrice: number, vatRate: number): number {
  // Calculate VAT amount from VAT-inclusive price
  // Formula: VAT = inclusive_price - (inclusive_price / (1 + vat_rate))
  return inclusivePrice - (inclusivePrice / (1 + vatRate));
}

// Get VAT rate for a specific product category
export function getVATRateForCategory(
  category: 'coffee' | 'accessories',
  settings: {
    use_category_vat?: boolean;
    vat_rate?: number;
    vat_rate_coffee?: number;
    vat_rate_accessories?: number;
  }
): number {
  // If category-specific VAT is not enabled, use general rate
  if (!settings.use_category_vat) {
    return settings.vat_rate || 0.077;
  }

  // Use category-specific rates
  if (category === 'coffee') {
    return settings.vat_rate_coffee || 0.077;
  } else {
    return settings.vat_rate_accessories || 0.077;
  }
}

// Calculate VAT breakdown for cart items by category
export function calculateVATByCategory(
  items: Array<{
    quantity: number;
    products: {
      price: number;
      discount_price?: number;
      category: 'coffee' | 'accessories';
    };
  }>,
  settings: {
    use_category_vat?: boolean;
    vat_rate?: number;
    vat_rate_coffee?: number;
    vat_rate_accessories?: number;
  }
): {
  totalVAT: number;
  coffeeVAT: number;
  accessoriesVAT: number;
  coffeeSubtotal: number;
  accessoriesSubtotal: number;
} {
  let coffeeSubtotal = 0;
  let accessoriesSubtotal = 0;

  // Calculate subtotals by category
  items.forEach(item => {
    const price = item.products.discount_price || item.products.price;
    const itemTotal = price * item.quantity;

    if (item.products.category === 'coffee') {
      coffeeSubtotal += itemTotal;
    } else {
      accessoriesSubtotal += itemTotal;
    }
  });

  // Calculate VAT for each category
  const coffeeVATRate = getVATRateForCategory('coffee', settings);
  const accessoriesVATRate = getVATRateForCategory('accessories', settings);

  const coffeeVAT = calculateVATFromInclusive(coffeeSubtotal, coffeeVATRate);
  const accessoriesVAT = calculateVATFromInclusive(accessoriesSubtotal, accessoriesVATRate);

  return {
    totalVAT: coffeeVAT + accessoriesVAT,
    coffeeVAT,
    accessoriesVAT,
    coffeeSubtotal,
    accessoriesSubtotal
  };
}

export function calculateExclusiveFromInclusive(inclusivePrice: number, vatRate: number): number {
  // Calculate exclusive price from VAT-inclusive price
  // Formula: exclusive_price = inclusive_price / (1 + vat_rate)
  return inclusivePrice / (1 + vatRate);
}

export function calculateInclusiveFromExclusive(exclusivePrice: number, vatRate: number): number {
  // Calculate VAT-inclusive price from exclusive price
  // Formula: inclusive_price = exclusive_price * (1 + vat_rate)
  return exclusivePrice * (1 + vatRate);
}

export function formatVATBreakdown(inclusivePrice: number, vatRate: number): {
  inclusive: number;
  exclusive: number;
  vatAmount: number;
  vatRate: number;
} {
  const exclusive = calculateExclusiveFromInclusive(inclusivePrice, vatRate);
  const vatAmount = calculateVATFromInclusive(inclusivePrice, vatRate);

  return {
    inclusive: inclusivePrice,
    exclusive: parseFloat(exclusive.toFixed(2)),
    vatAmount: parseFloat(vatAmount.toFixed(2)),
    vatRate: vatRate
  };
}

// Debounce function for search
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Generate random coupon code
export function generateCouponCode(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Validate coupon code format
export function isValidCouponCode(code: string): boolean {
  return /^[A-Z0-9]{4,12}$/.test(code);
}
