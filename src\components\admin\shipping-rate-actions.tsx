'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { MoreHorizontal, Edit, Trash2, ToggleLeft, ToggleRight } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'

interface ShippingRate {
  id: string
  country: string
  cost: number
  free_shipping_threshold?: number
  estimated_days: string
  is_active: boolean
}

interface ShippingRateActionsProps {
  rate: ShippingRate
}

export function ShippingRateActions({ rate }: ShippingRateActionsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  const handleToggleActive = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/shipping/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: rate.id,
          is_active: !rate.is_active,
        }),
      })

      if (response.ok) {
        toast({
          title: 'Erfolg',
          description: `Versandrate wurde ${!rate.is_active ? 'aktiviert' : 'deaktiviert'}.`,
        })
        router.refresh()
      } else {
        throw new Error('Fehler beim Aktualisieren')
      }
    } catch {
      toast({
        title: 'Fehler',
        description: 'Versandrate konnte nicht aktualisiert werden.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/shipping/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: rate.id }),
      })

      if (response.ok) {
        toast({
          title: 'Erfolg',
          description: 'Versandrate wurde gelöscht.',
        })
        router.refresh()
      } else {
        throw new Error('Fehler beim Löschen')
      }
    } catch {
      toast({
        title: 'Fehler',
        description: 'Versandrate konnte nicht gelöscht werden.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
      setShowDeleteDialog(false)
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Menü öffnen</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={() => router.push(`/admin/shipping/edit/${rate.id}`)}
          >
            <Edit className="mr-2 h-4 w-4" />
            Bearbeiten
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleToggleActive} disabled={loading}>
            {rate.is_active ? (
              <>
                <ToggleLeft className="mr-2 h-4 w-4" />
                Deaktivieren
              </>
            ) : (
              <>
                <ToggleRight className="mr-2 h-4 w-4" />
                Aktivieren
              </>
            )}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => setShowDeleteDialog(true)}
            className="text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Löschen
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Versandrate löschen</AlertDialogTitle>
            <AlertDialogDescription>
              Sind Sie sicher, dass Sie die Versandrate für &quot;{rate.country}&quot; löschen möchten?
              Diese Aktion kann nicht rückgängig gemacht werden.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Abbrechen</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={loading}
            >
              {loading ? 'Wird gelöscht...' : 'Löschen'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
