-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE user_address_type AS ENUM ('billing', 'shipping');
CREATE TYPE product_category AS ENUM ('coffee', 'accessories');
CREATE TYPE coffee_type AS ENUM ('capsules', 'pods', 'beans', 'ground');
CREATE TYPE order_status AS ENUM ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled');
CREATE TYPE payment_status AS ENUM ('pending', 'paid', 'failed', 'refunded');
CREATE TYPE coupon_type AS ENUM ('percentage', 'fixed_amount');

-- Users table (extends Supabase auth.users)
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    phone TEXT,
    date_of_birth DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    lifetime_spend DECIMAL(10,2) DEFAULT 0,
    current_level INTEGER DEFAULT 1,
    total_points INTEGER DEFAULT 0,
    is_admin BOOLEAN DEFAULT FALSE
);

-- User addresses
CREATE TABLE user_addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type user_address_type NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    company TEXT,
    street_address TEXT NOT NULL,
    city TEXT NOT NULL,
    postal_code TEXT NOT NULL,
    country TEXT NOT NULL DEFAULT 'CH',
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Products
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    category product_category NOT NULL,
    coffee_type coffee_type,
    brand TEXT,
    blend TEXT,
    machine_compatibility TEXT[],
    pack_quantity INTEGER,
    pack_weight_grams INTEGER,
    price DECIMAL(10,2) NOT NULL,
    discount_price DECIMAL(10,2),
    cost_per_espresso DECIMAL(10,4),
    images TEXT[] DEFAULT '{}',
    inventory_count INTEGER DEFAULT 0,
    purchase_cost DECIMAL(10,2),
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bundles
CREATE TABLE bundles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    image TEXT NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    discount_price DECIMAL(10,2),
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bundle items
CREATE TABLE bundle_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bundle_id UUID NOT NULL REFERENCES bundles(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1
);

-- Carts
CREATE TABLE carts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_id TEXT,
    status TEXT DEFAULT 'active',
    total_amount DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT cart_user_or_session CHECK (
        (user_id IS NOT NULL AND session_id IS NULL) OR
        (user_id IS NULL AND session_id IS NOT NULL)
    )
);

-- Cart items
CREATE TABLE cart_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cart_id UUID NOT NULL REFERENCES carts(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Orders
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_number VARCHAR(10) UNIQUE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    email TEXT NOT NULL,
    status order_status DEFAULT 'pending',
    subtotal DECIMAL(10,2) NOT NULL,
    shipping_cost DECIMAL(10,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'CHF',
    payment_status payment_status DEFAULT 'pending',
    payment_intent_id TEXT,
    shipping_address JSONB NOT NULL,
    billing_address JSONB NOT NULL,
    tracking_number TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Order items
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE RESTRICT,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL
);

-- Coupons
CREATE TABLE coupons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code TEXT NOT NULL UNIQUE,
    type coupon_type NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    minimum_order_amount DECIMAL(10,2),
    usage_limit INTEGER,
    used_count INTEGER DEFAULT 0,
    valid_from TIMESTAMPTZ DEFAULT NOW(),
    valid_until TIMESTAMPTZ NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User levels
CREATE TABLE user_levels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    level INTEGER NOT NULL UNIQUE,
    name TEXT NOT NULL,
    minimum_points INTEGER NOT NULL DEFAULT 0,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    points_multiplier DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Gift thresholds
CREATE TABLE gift_thresholds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    threshold_amount DECIMAL(10,2) NOT NULL,
    gift_product_ids UUID[] NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Level management configuration
CREATE TABLE level_management_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    total_levels INTEGER NOT NULL DEFAULT 4,
    multiplier_mode VARCHAR(20) NOT NULL DEFAULT 'uniform' CHECK (multiplier_mode IN ('uniform', 'incremental')),
    uniform_multiplier DECIMAL(3,2) DEFAULT 1.0,
    incremental_multiplier_start DECIMAL(3,2) DEFAULT 1.0,
    incremental_multiplier_step DECIMAL(3,2) DEFAULT 0.1,
    point_progression_type VARCHAR(20) NOT NULL DEFAULT 'linear' CHECK (point_progression_type IN ('linear', 'exponential', 'custom')),
    base_point_increment INTEGER DEFAULT 200,
    exponential_factor DECIMAL(3,2) DEFAULT 1.5,
    custom_progression_formula TEXT,
    starting_points INTEGER DEFAULT 0,
    discount_progression_type VARCHAR(20) NOT NULL DEFAULT 'linear' CHECK (discount_progression_type IN ('linear', 'exponential', 'custom')),
    base_discount_increment DECIMAL(3,2) DEFAULT 2.5,
    max_discount_percentage DECIMAL(5,2) DEFAULT 20.0,
    level_naming_pattern VARCHAR(50) DEFAULT 'Level {level}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Site settings
CREATE TABLE site_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    -- Store settings
    store_name TEXT DEFAULT 'PrimeCaffe',
    store_description TEXT DEFAULT '',
    contact_email TEXT DEFAULT '<EMAIL>',
    contact_phone TEXT DEFAULT '+41 44 123 45 67',

    -- Email settings
    email_order_confirmation BOOLEAN DEFAULT TRUE,
    email_shipping_notification BOOLEAN DEFAULT TRUE,
    email_newsletter BOOLEAN DEFAULT FALSE,
    smtp_server TEXT DEFAULT '',
    smtp_port INTEGER DEFAULT 587,
    smtp_username TEXT DEFAULT '',
    smtp_password TEXT DEFAULT '',

    -- Payment settings (only Stripe is functional)
    payment_stripe_enabled BOOLEAN DEFAULT TRUE,
    payment_paypal_enabled BOOLEAN DEFAULT FALSE, -- Not implemented
    payment_twint_enabled BOOLEAN DEFAULT FALSE,  -- Not implemented
    payment_invoice_enabled BOOLEAN DEFAULT FALSE, -- Not implemented

    -- Security settings (not implemented)
    security_2fa_enabled BOOLEAN DEFAULT FALSE,
    security_rate_limiting BOOLEAN DEFAULT TRUE,
    security_session_timeout INTEGER DEFAULT 60,

    -- Localization
    default_language TEXT DEFAULT 'de',
    default_currency TEXT DEFAULT 'CHF',
    timezone TEXT DEFAULT 'Europe/Zurich',

    -- VAT/Tax settings
    vat_rate DECIMAL(5,4) DEFAULT 0.077, -- 7.7% Swiss VAT
    vat_included_in_prices BOOLEAN DEFAULT TRUE, -- Prices are VAT-inclusive
    vat_number TEXT, -- Company VAT number

    -- Notification settings (not implemented)
    notifications_low_stock BOOLEAN DEFAULT TRUE,
    notifications_new_orders BOOLEAN DEFAULT TRUE,
    notifications_system_alerts BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Shipping rates
CREATE TABLE shipping_rates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    country TEXT NOT NULL,
    cost DECIMAL(10,2) NOT NULL,
    free_shipping_threshold DECIMAL(10,2),
    estimated_days TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_is_admin ON users(is_admin);
CREATE INDEX idx_user_addresses_user_id ON user_addresses(user_id);
CREATE INDEX idx_user_addresses_type ON user_addresses(type);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_coffee_type ON products(coffee_type);
CREATE INDEX idx_products_is_available ON products(is_available);
CREATE INDEX idx_products_slug ON products(slug);
CREATE INDEX idx_bundles_slug ON bundles(slug);
CREATE INDEX idx_bundles_is_available ON bundles(is_available);
CREATE INDEX idx_carts_user_id ON carts(user_id);
CREATE INDEX idx_carts_session_id ON carts(session_id);
CREATE INDEX idx_carts_status ON carts(status);
CREATE INDEX idx_cart_items_cart_id ON cart_items(cart_id);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_payment_status ON orders(payment_status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_coupons_code ON coupons(code);
CREATE INDEX idx_coupons_is_active ON coupons(is_active);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_addresses_updated_at BEFORE UPDATE ON user_addresses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bundles_updated_at BEFORE UPDATE ON bundles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_carts_updated_at BEFORE UPDATE ON carts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cart_items_updated_at BEFORE UPDATE ON cart_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_coupons_updated_at BEFORE UPDATE ON coupons FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_levels_updated_at BEFORE UPDATE ON user_levels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_gift_thresholds_updated_at BEFORE UPDATE ON gift_thresholds FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_shipping_rates_updated_at BEFORE UPDATE ON shipping_rates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Order number generation
-- Create sequence for order numbers (resets yearly)
CREATE SEQUENCE order_number_seq START 1;

-- Function to generate formatted order number
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    current_year TEXT;
    next_number INTEGER;
    order_number TEXT;
BEGIN
    -- Get current year (last 2 digits)
    current_year := RIGHT(EXTRACT(YEAR FROM NOW())::TEXT, 2);

    -- Get next sequence number
    next_number := nextval('order_number_seq');

    -- Format as PC + year + 4-digit number
    order_number := 'PC' || current_year || LPAD(next_number::TEXT, 4, '0');

    RETURN order_number;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically generate order number on insert
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_order_number_trigger
    BEFORE INSERT ON orders
    FOR EACH ROW
    EXECUTE FUNCTION set_order_number();

-- Auto-create user profile when auth user is created
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, first_name, last_name, phone, is_admin, total_points, lifetime_spend, current_level)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
        NEW.raw_user_meta_data->>'phone',
        FALSE,
        0,
        0,
        1
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user profile
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();
