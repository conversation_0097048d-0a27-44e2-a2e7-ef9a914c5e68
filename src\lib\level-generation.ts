import { LevelManagementConfig } from '@/app/api/admin/gamification/level-config/route'

export interface GeneratedLevel {
  level: number
  name: string
  minimum_points: number
  discount_percentage: number
  points_multiplier: number
}

export interface LevelGenerationStats {
  totalLevels: number
  maxPoints: number
  maxDiscount: number
  maxMultiplier: number
  averagePointsPerLevel: number
  pointsGrowthRate: number
  discountGrowthRate: number
}

/**
 * Calculate point requirements for a specific level
 */
export function calculatePointRequirement(
  level: number,
  config: LevelManagementConfig
): number {
  if (level === 1) return config.starting_points

  switch (config.point_progression_type) {
    case 'linear':
      return config.starting_points + (level - 1) * config.base_point_increment

    case 'exponential':
      return Math.floor(
        config.starting_points + 
        config.base_point_increment * Math.pow(config.exponential_factor, level - 2)
      )

    case 'custom':
      // Custom formula evaluation would go here
      // For now, fallback to linear progression
      return config.starting_points + (level - 1) * config.base_point_increment

    default:
      return config.starting_points + (level - 1) * config.base_point_increment
  }
}

/**
 * Calculate discount percentage for a specific level
 */
export function calculateDiscountPercentage(
  level: number,
  config: LevelManagementConfig
): number {
  if (level === 1) return 0

  let discount: number
  switch (config.discount_progression_type) {
    case 'linear':
      discount = (level - 1) * config.base_discount_increment
      break

    case 'exponential':
      discount = config.base_discount_increment * Math.pow(1.5, level - 2)
      break

    case 'custom':
      // Custom formula evaluation would go here
      // For now, fallback to linear progression
      discount = (level - 1) * config.base_discount_increment
      break

    default:
      discount = (level - 1) * config.base_discount_increment
  }

  return Math.min(discount, config.max_discount_percentage)
}

/**
 * Calculate points multiplier for a specific level
 */
export function calculatePointsMultiplier(
  level: number,
  config: LevelManagementConfig
): number {
  switch (config.multiplier_mode) {
    case 'uniform':
      return config.uniform_multiplier

    case 'incremental':
      return Math.round(
        (config.incremental_multiplier_start + 
         (level - 1) * config.incremental_multiplier_step) * 100
      ) / 100 // Round to 2 decimal places

    default:
      return config.uniform_multiplier
  }
}

/**
 * Generate level name based on pattern
 */
export function generateLevelName(level: number, pattern: string): string {
  // Support multiple placeholders
  return pattern
    .replace('{level}', level.toString())
    .replace('{Level}', level.toString())
    .replace('{LEVEL}', level.toString())
}

/**
 * Validate level management configuration
 */
export function validateLevelConfig(config: LevelManagementConfig): string[] {
  const errors: string[] = []

  if (config.total_levels < 1 || config.total_levels > 1000) {
    errors.push('Total levels must be between 1 and 1000')
  }

  if (config.multiplier_mode === 'uniform') {
    if (config.uniform_multiplier < 0.1 || config.uniform_multiplier > 10) {
      errors.push('Uniform multiplier must be between 0.1 and 10')
    }
  } else if (config.multiplier_mode === 'incremental') {
    if (config.incremental_multiplier_start < 0.1 || config.incremental_multiplier_start > 10) {
      errors.push('Incremental multiplier start must be between 0.1 and 10')
    }
    if (config.incremental_multiplier_step < 0 || config.incremental_multiplier_step > 1) {
      errors.push('Incremental multiplier step must be between 0 and 1')
    }
  }

  if (config.base_point_increment < 1) {
    errors.push('Base point increment must be at least 1')
  }

  if (config.starting_points < 0) {
    errors.push('Starting points cannot be negative')
  }

  if (config.max_discount_percentage < 0 || config.max_discount_percentage > 100) {
    errors.push('Max discount percentage must be between 0 and 100')
  }

  if (config.base_discount_increment < 0) {
    errors.push('Base discount increment cannot be negative')
  }

  if (config.point_progression_type === 'exponential' && 
      (config.exponential_factor < 1.1 || config.exponential_factor > 3)) {
    errors.push('Exponential factor must be between 1.1 and 3')
  }

  return errors
}

/**
 * Generate all levels based on configuration
 */
export function generateLevels(config: LevelManagementConfig): GeneratedLevel[] {
  const levels: GeneratedLevel[] = []

  for (let i = 1; i <= config.total_levels; i++) {
    levels.push({
      level: i,
      name: generateLevelName(i, config.level_naming_pattern),
      minimum_points: calculatePointRequirement(i, config),
      discount_percentage: calculateDiscountPercentage(i, config),
      points_multiplier: calculatePointsMultiplier(i, config)
    })
  }

  return levels
}

/**
 * Calculate statistics for generated levels
 */
export function calculateLevelStats(levels: GeneratedLevel[]): LevelGenerationStats {
  if (levels.length === 0) {
    return {
      totalLevels: 0,
      maxPoints: 0,
      maxDiscount: 0,
      maxMultiplier: 0,
      averagePointsPerLevel: 0,
      pointsGrowthRate: 0,
      discountGrowthRate: 0
    }
  }

  const maxPoints = Math.max(...levels.map(l => l.minimum_points))
  const maxDiscount = Math.max(...levels.map(l => l.discount_percentage))
  const maxMultiplier = Math.max(...levels.map(l => l.points_multiplier))

  const averagePointsPerLevel = levels.length > 1 
    ? Math.floor((levels[levels.length - 1].minimum_points - levels[0].minimum_points) / (levels.length - 1))
    : 0

  // Calculate growth rates
  const pointsGrowthRate = levels.length > 1
    ? ((levels[levels.length - 1].minimum_points / Math.max(levels[0].minimum_points, 1)) - 1) * 100
    : 0

  const discountGrowthRate = levels.length > 1 && levels[0].discount_percentage > 0
    ? ((levels[levels.length - 1].discount_percentage / levels[0].discount_percentage) - 1) * 100
    : maxDiscount

  return {
    totalLevels: levels.length,
    maxPoints,
    maxDiscount,
    maxMultiplier,
    averagePointsPerLevel,
    pointsGrowthRate: Math.round(pointsGrowthRate * 100) / 100,
    discountGrowthRate: Math.round(discountGrowthRate * 100) / 100
  }
}

/**
 * Get default level management configuration
 */
export function getDefaultLevelConfig(): LevelManagementConfig {
  return {
    total_levels: 4,
    multiplier_mode: 'incremental',
    uniform_multiplier: 1.0,
    incremental_multiplier_start: 1.0,
    incremental_multiplier_step: 0.2,
    point_progression_type: 'linear',
    base_point_increment: 200,
    exponential_factor: 1.5,
    starting_points: 0,
    discount_progression_type: 'linear',
    base_discount_increment: 5.0,
    max_discount_percentage: 20.0,
    level_naming_pattern: 'Level {level}',
    is_active: true
  }
}
