'use client'

import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Settings, Eye, Zap, TrendingUp, BarChart3, Info, Gift, Calculator, Target } from 'lucide-react'
import { LevelManagementConfig } from '@/app/api/admin/gamification/level-config/route'
import { GeneratedLevel, LevelGenerationStats } from '@/lib/level-generation'

interface AdvancedLevelManagementProps {
  onLevelsGenerated?: () => void
}

export default function AdvancedLevelManagement({ onLevelsGenerated }: AdvancedLevelManagementProps) {
  const t = useTranslations('admin')
  const { toast } = useToast()

  const [loading, setLoading] = useState(false)
  const [previewLoading, setPreviewLoading] = useState(false)
  const [config, setConfig] = useState<LevelManagementConfig>({
    total_levels: 4,
    multiplier_mode: 'incremental',
    uniform_multiplier: 1.0,
    incremental_multiplier_start: 1.0,
    incremental_multiplier_step: 0.2,
    point_progression_type: 'linear',
    base_point_increment: 200,
    exponential_factor: 1.5,
    starting_points: 0,
    discount_progression_type: 'linear',
    base_discount_increment: 5.0,
    max_discount_percentage: 20.0,
    level_naming_pattern: 'Level {level}',
    is_active: true
  })
  
  const [previewLevels, setPreviewLevels] = useState<GeneratedLevel[]>([])
  const [previewStats, setPreviewStats] = useState<LevelGenerationStats | null>(null)
  const [showPreview, setShowPreview] = useState(false)

  // Load existing configuration on mount
  useEffect(() => {
    loadConfiguration()
  }, [])

  const loadConfiguration = async () => {
    try {
      const response = await fetch('/api/admin/gamification/level-config')
      if (response.ok) {
        const data = await response.json()
        if (data.config) {
          setConfig(data.config)
        }
      }
    } catch (error) {
      console.error('Error loading configuration:', error)
    }
  }

  const handleConfigChange = (field: keyof LevelManagementConfig, value: string | number | boolean) => {
    setConfig(prev => ({ ...prev, [field]: value }))
    setShowPreview(false) // Hide preview when config changes
  }

  const generatePreview = async () => {
    setPreviewLoading(true)
    try {
      const response = await fetch('/api/admin/gamification/level-preview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      })

      if (response.ok) {
        const data = await response.json()
        setPreviewLevels(data.levels)
        setPreviewStats(data.stats)
        setShowPreview(true)
      } else {
        const error = await response.json()
        toast({
          title: t('levelManagement.previewError'),
          description: error.error,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error generating preview:', error)
      toast({
        title: t('levelManagement.previewError'),
        description: t('levelManagement.previewErrorMessage'),
        variant: 'destructive'
      })
    } finally {
      setPreviewLoading(false)
    }
  }

  const saveConfiguration = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/gamification/level-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      })

      if (response.ok) {
        toast({
          title: t('levelManagement.configSaved'),
          description: t('levelManagement.configSavedMessage')
        })
      } else {
        const error = await response.json()
        toast({
          title: t('levelManagement.configError'),
          description: error.error,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error saving configuration:', error)
      toast({
        title: t('levelManagement.configError'),
        description: t('levelManagement.configErrorMessage'),
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const applyLevels = async () => {
    if (!config.id) {
      toast({
        title: t('levelManagement.applyError'),
        description: t('levelManagement.saveConfigFirst'),
        variant: 'destructive'
      })
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/admin/gamification/level-generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ configId: config.id })
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: t('levelManagement.levelsGenerated'),
          description: data.message
        })
        onLevelsGenerated?.()
      } else {
        const error = await response.json()
        toast({
          title: t('levelManagement.generateError'),
          description: error.error,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error applying levels:', error)
      toast({
        title: t('levelManagement.generateError'),
        description: t('levelManagement.generateErrorMessage'),
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* Header with Info */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Settings className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">{t('levelManagement.title')}</h1>
            <p className="text-muted-foreground">{t('levelManagement.subtitle')}</p>
          </div>
        </div>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {t('levelManagement.infoMessage')}
          </AlertDescription>
        </Alert>
      </div>

      {/* Configuration Tabs */}
      <Tabs defaultValue="basic" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            {t('levelManagement.basicConfig')}
          </TabsTrigger>
          <TabsTrigger value="points" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            {t('levelManagement.pointsConfig')}
          </TabsTrigger>
          <TabsTrigger value="rewards" className="flex items-center gap-2">
            <Gift className="h-4 w-4" />
            {t('levelManagement.rewardsConfig')}
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            {t('levelManagement.preview')}
          </TabsTrigger>
        </TabsList>

        {/* Basic Configuration Tab */}
        <TabsContent value="basic">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                {t('levelManagement.basicSettings')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="total-levels" className="text-base font-medium">
                    {t('levelManagement.totalLevels')}
                  </Label>
                  <Input
                    id="total-levels"
                    type="number"
                    min="1"
                    max="1000"
                    value={config.total_levels}
                    onChange={(e) => handleConfigChange('total_levels', parseInt(e.target.value))}
                    className="text-lg"
                  />
                  <p className="text-sm text-muted-foreground">
                    {t('levelManagement.totalLevelsDesc')}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="level-naming" className="text-base font-medium">
                    {t('levelManagement.levelNaming')}
                  </Label>
                  <Input
                    id="level-naming"
                    value={config.level_naming_pattern}
                    onChange={(e) => handleConfigChange('level_naming_pattern', e.target.value)}
                    placeholder="Level {level}"
                    className="text-lg"
                  />
                  <p className="text-sm text-muted-foreground">
                    {t('levelManagement.levelNamingDesc')}
                  </p>
                  <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                    <strong>{t('levelManagement.example')}:</strong> &ldquo;{config.level_naming_pattern.replace('{level}', '1')}&rdquo;, &ldquo;{config.level_naming_pattern.replace('{level}', '2')}&rdquo;
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Points Configuration Tab */}
        <TabsContent value="points">
          <div className="space-y-6">
            {/* Point Multipliers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  {t('levelManagement.multiplierConfig')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="multiplier-mode" className="text-base font-medium">
                      {t('levelManagement.multiplierMode')}
                    </Label>
                    <Select
                      value={config.multiplier_mode}
                      onValueChange={(value: 'uniform' | 'incremental') =>
                        handleConfigChange('multiplier_mode', value)
                      }
                    >
                      <SelectTrigger className="text-lg">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="uniform">
                          <div className="space-y-1">
                            <div className="font-medium">{t('levelManagement.uniformMultiplier')}</div>
                            <div className="text-xs text-muted-foreground">{t('levelManagement.uniformMultiplierDesc')}</div>
                          </div>
                        </SelectItem>
                        <SelectItem value="incremental">
                          <div className="space-y-1">
                            <div className="font-medium">{t('levelManagement.incrementalMultiplier')}</div>
                            <div className="text-xs text-muted-foreground">{t('levelManagement.incrementalMultiplierDesc')}</div>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {config.multiplier_mode === 'uniform' ? (
                    <div className="space-y-2">
                      <Label htmlFor="uniform-multiplier" className="text-base font-medium">
                        {t('levelManagement.uniformValue')}
                      </Label>
                      <Input
                        id="uniform-multiplier"
                        type="number"
                        step="0.1"
                        min="0.1"
                        max="10"
                        value={config.uniform_multiplier}
                        onChange={(e) => handleConfigChange('uniform_multiplier', parseFloat(e.target.value))}
                        className="text-lg"
                      />
                      <p className="text-sm text-muted-foreground">
                        {t('levelManagement.uniformValueDesc')}
                      </p>
                    </div>
                  ) : (
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="incremental-start" className="text-base font-medium">
                          {t('levelManagement.incrementalStart')}
                        </Label>
                        <Input
                          id="incremental-start"
                          type="number"
                          step="0.1"
                          min="0.1"
                          max="10"
                          value={config.incremental_multiplier_start}
                          onChange={(e) => handleConfigChange('incremental_multiplier_start', parseFloat(e.target.value))}
                          className="text-lg"
                        />
                        <p className="text-sm text-muted-foreground">
                          {t('levelManagement.incrementalStartDesc')}
                        </p>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="incremental-step" className="text-base font-medium">
                          {t('levelManagement.incrementalStep')}
                        </Label>
                        <Input
                          id="incremental-step"
                          type="number"
                          step="0.1"
                          min="0.1"
                          max="2"
                          value={config.incremental_multiplier_step}
                          onChange={(e) => handleConfigChange('incremental_multiplier_step', parseFloat(e.target.value))}
                          className="text-lg"
                        />
                        <p className="text-sm text-muted-foreground">
                          {t('levelManagement.incrementalStepDesc')}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Point Progression */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  {t('levelManagement.pointProgression')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="progression-type" className="text-base font-medium">
                      {t('levelManagement.progressionType')}
                    </Label>
                    <Select
                      value={config.point_progression_type}
                      onValueChange={(value: 'linear' | 'exponential' | 'custom') =>
                        handleConfigChange('point_progression_type', value)
                      }
                    >
                      <SelectTrigger className="text-lg">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="linear">
                          <div className="space-y-1">
                            <div className="font-medium">{t('levelManagement.linear')}</div>
                            <div className="text-xs text-muted-foreground">{t('levelManagement.linearDesc')}</div>
                          </div>
                        </SelectItem>
                        <SelectItem value="exponential">
                          <div className="space-y-1">
                            <div className="font-medium">{t('levelManagement.exponential')}</div>
                            <div className="text-xs text-muted-foreground">{t('levelManagement.exponentialDesc')}</div>
                          </div>
                        </SelectItem>
                        <SelectItem value="custom" disabled>
                          <div className="space-y-1">
                            <div className="font-medium text-muted-foreground">{t('levelManagement.custom')}</div>
                            <div className="text-xs text-muted-foreground">{t('levelManagement.customDesc')}</div>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="starting-points" className="text-base font-medium">
                        {t('levelManagement.startingPoints')}
                      </Label>
                      <Input
                        id="starting-points"
                        type="number"
                        min="0"
                        value={config.starting_points}
                        onChange={(e) => handleConfigChange('starting_points', parseInt(e.target.value))}
                        className="text-lg"
                      />
                      <p className="text-sm text-muted-foreground">
                        {t('levelManagement.startingPointsDesc')}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="base-increment" className="text-base font-medium">
                        {t('levelManagement.baseIncrement')}
                      </Label>
                      <Input
                        id="base-increment"
                        type="number"
                        min="1"
                        value={config.base_point_increment}
                        onChange={(e) => handleConfigChange('base_point_increment', parseInt(e.target.value))}
                        className="text-lg"
                      />
                      <p className="text-sm text-muted-foreground">
                        {t('levelManagement.baseIncrementDesc')}
                      </p>
                    </div>
                  </div>

                  {config.point_progression_type === 'exponential' && (
                    <div className="space-y-2">
                      <Label htmlFor="exponential-factor" className="text-base font-medium">
                        {t('levelManagement.exponentialFactor')}
                      </Label>
                      <Input
                        id="exponential-factor"
                        type="number"
                        step="0.1"
                        min="1.1"
                        max="3"
                        value={config.exponential_factor}
                        onChange={(e) => handleConfigChange('exponential_factor', parseFloat(e.target.value))}
                        className="text-lg"
                      />
                      <p className="text-sm text-muted-foreground">
                        {t('levelManagement.exponentialFactorDesc')}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Rewards Configuration Tab */}
        <TabsContent value="rewards">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gift className="h-5 w-5" />
                {t('levelManagement.discountConfig')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="discount-progression" className="text-base font-medium">
                    {t('levelManagement.discountProgression')}
                  </Label>
                  <Select
                    value={config.discount_progression_type}
                    onValueChange={(value: 'linear' | 'exponential' | 'custom') =>
                      handleConfigChange('discount_progression_type', value)
                    }
                  >
                    <SelectTrigger className="text-lg">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="linear">
                        <div className="space-y-1">
                          <div className="font-medium">{t('levelManagement.linear')}</div>
                          <div className="text-xs text-muted-foreground">{t('levelManagement.linearDiscountDesc')}</div>
                        </div>
                      </SelectItem>
                      <SelectItem value="exponential">
                        <div className="space-y-1">
                          <div className="font-medium">{t('levelManagement.exponential')}</div>
                          <div className="text-xs text-muted-foreground">{t('levelManagement.exponentialDiscountDesc')}</div>
                        </div>
                      </SelectItem>
                      <SelectItem value="custom" disabled>
                        <div className="space-y-1">
                          <div className="font-medium text-muted-foreground">{t('levelManagement.custom')}</div>
                          <div className="text-xs text-muted-foreground">{t('levelManagement.customDiscountDesc')}</div>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="base-discount" className="text-base font-medium">
                      {t('levelManagement.baseDiscountIncrement')}
                    </Label>
                    <Input
                      id="base-discount"
                      type="number"
                      step="0.1"
                      min="0"
                      value={config.base_discount_increment}
                      onChange={(e) => handleConfigChange('base_discount_increment', parseFloat(e.target.value))}
                      className="text-lg"
                    />
                    <p className="text-sm text-muted-foreground">
                      {t('levelManagement.baseDiscountIncrementDesc')}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="max-discount" className="text-base font-medium">
                      {t('levelManagement.maxDiscount')}
                    </Label>
                    <Input
                      id="max-discount"
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      value={config.max_discount_percentage}
                      onChange={(e) => handleConfigChange('max_discount_percentage', parseFloat(e.target.value))}
                      className="text-lg"
                    />
                    <p className="text-sm text-muted-foreground">
                      {t('levelManagement.maxDiscountDesc')}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Preview Tab */}
        <TabsContent value="preview">
          <div className="space-y-6">
            {/* Preview Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  {t('levelManagement.previewAndActions')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-4">
                  <Button onClick={generatePreview} disabled={previewLoading} size="lg">
                    <Eye className="mr-2 h-4 w-4" />
                    {previewLoading ? t('levelManagement.generating') : t('levelManagement.generatePreview')}
                  </Button>
                  <Button onClick={saveConfiguration} disabled={loading} variant="outline" size="lg">
                    {loading ? t('saving') : t('levelManagement.saveConfig')}
                  </Button>
                  {config.id && (
                    <Button onClick={applyLevels} disabled={loading} variant="default" size="lg">
                      <Zap className="mr-2 h-4 w-4" />
                      {loading ? t('levelManagement.applying') : t('levelManagement.applyLevels')}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Preview Results */}
            {showPreview && previewLevels.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    {t('levelManagement.previewResults')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Statistics */}
                  {previewStats && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="p-6 border rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 text-center">
                        <BarChart3 className="h-8 w-8 mx-auto mb-3 text-blue-600 dark:text-blue-400" />
                        <p className="text-sm text-muted-foreground mb-1">{t('levelManagement.totalLevels')}</p>
                        <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{previewStats.totalLevels}</p>
                      </div>
                      <div className="p-6 border rounded-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 text-center">
                        <TrendingUp className="h-8 w-8 mx-auto mb-3 text-green-600 dark:text-green-400" />
                        <p className="text-sm text-muted-foreground mb-1">{t('levelManagement.maxPoints')}</p>
                        <p className="text-2xl font-bold text-green-600 dark:text-green-400">{previewStats.maxPoints.toLocaleString()}</p>
                      </div>
                      <div className="p-6 border rounded-lg bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 text-center">
                        <Gift className="h-8 w-8 mx-auto mb-3 text-purple-600 dark:text-purple-400" />
                        <p className="text-sm text-muted-foreground mb-1">{t('levelManagement.maxDiscount')}</p>
                        <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{previewStats.maxDiscount.toFixed(1)}%</p>
                      </div>
                      <div className="p-6 border rounded-lg bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 text-center">
                        <Zap className="h-8 w-8 mx-auto mb-3 text-orange-600 dark:text-orange-400" />
                        <p className="text-sm text-muted-foreground mb-1">{t('levelManagement.maxMultiplier')}</p>
                        <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{previewStats.maxMultiplier.toFixed(2)}x</p>
                      </div>
                    </div>
                  )}

                  {/* Level Preview Table */}
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-border rounded-lg overflow-hidden">
                      <thead>
                        <tr className="bg-muted/50">
                          <th className="border border-border px-6 py-4 text-left font-semibold">{t('levelManagement.level')}</th>
                          <th className="border border-border px-6 py-4 text-left font-semibold">{t('levelManagement.name')}</th>
                          <th className="border border-border px-6 py-4 text-left font-semibold">{t('levelManagement.minPoints')}</th>
                          <th className="border border-border px-6 py-4 text-left font-semibold">{t('levelManagement.discount')}</th>
                          <th className="border border-border px-6 py-4 text-left font-semibold">{t('levelManagement.multiplier')}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {previewLevels.slice(0, 10).map((level, index) => (
                          <tr key={level.level} className={`hover:bg-muted/30 transition-colors ${index % 2 === 0 ? 'bg-background' : 'bg-muted/20'}`}>
                            <td className="border border-border px-6 py-4">
                              <Badge variant="outline" className="font-mono">{level.level}</Badge>
                            </td>
                            <td className="border border-border px-6 py-4 font-medium">{level.name}</td>
                            <td className="border border-border px-6 py-4 font-mono">{level.minimum_points.toLocaleString()}</td>
                            <td className="border border-border px-6 py-4">
                              <Badge variant="secondary" className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                {level.discount_percentage.toFixed(1)}%
                              </Badge>
                            </td>
                            <td className="border border-border px-6 py-4">
                              <Badge variant="secondary" className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                {level.points_multiplier.toFixed(2)}x
                              </Badge>
                            </td>
                          </tr>
                        ))}
                        {previewLevels.length > 10 && (
                          <tr>
                            <td colSpan={5} className="border border-border px-6 py-4 text-center text-muted-foreground bg-muted/30">
                              <div className="flex items-center justify-center gap-2">
                                <Info className="h-4 w-4" />
                                {t('levelManagement.showingFirst10', { total: previewLevels.length })}
                              </div>
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* No Preview Message */}
            {!showPreview && (
              <Card>
                <CardContent className="py-12 text-center">
                  <Eye className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">{t('levelManagement.noPreview')}</h3>
                  <p className="text-muted-foreground mb-4">{t('levelManagement.noPreviewDesc')}</p>
                  <Button onClick={generatePreview} disabled={previewLoading}>
                    <Eye className="mr-2 h-4 w-4" />
                    {t('levelManagement.generatePreview')}
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
