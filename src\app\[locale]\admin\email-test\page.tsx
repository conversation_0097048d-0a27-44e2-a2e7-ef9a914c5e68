'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, CheckCircle, Mail, Send, Settings } from 'lucide-react'
import { toast } from 'sonner'

interface TestResult {
  success: boolean
  message: string
  result?: {
    messageId?: string
    response?: string
    accepted?: string[]
    rejected?: string[]
  }
  error?: {
    name?: string
    message?: string
    stack?: string
    code?: string
    command?: string
    response?: string
    responseCode?: number
  }
}

export default function EmailTestPage() {
  const [testing, setTesting] = useState(false)
  const [testingOrder, setTestingOrder] = useState(false)
  const [testResult, setTestResult] = useState<TestResult | null>(null)
  const [orderTestResult, setOrderTestResult] = useState<TestResult | null>(null)

  const runEmailTest = async () => {
    setTesting(true)
    setTestResult(null)

    try {
      const response = await fetch('/api/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const result = await response.json()
      setTestResult(result)

      if (result.success) {
        toast.success('Email test completed successfully!')
      } else {
        toast.error('Email test failed')
      }

    } catch (error) {
      console.error('Error running email test:', error)
      setTestResult({
        success: false,
        message: 'Failed to run email test',
        error: {
          message: error instanceof Error ? error.message : String(error)
        }
      })
      toast.error('Failed to run email test')
    } finally {
      setTesting(false)
    }
  }

  const runOrderEmailTest = async () => {
    setTestingOrder(true)
    setOrderTestResult(null)

    try {
      const response = await fetch('/api/test-order-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const result = await response.json()
      setOrderTestResult(result)

      if (result.success) {
        toast.success('Order email test completed successfully!')
      } else {
        toast.error('Order email test failed')
      }

    } catch (error) {
      console.error('Error running order email test:', error)
      setOrderTestResult({
        success: false,
        message: 'Failed to run order email test',
        error: {
          message: error instanceof Error ? error.message : String(error)
        }
      })
      toast.error('Failed to run order email test')
    } finally {
      setTestingOrder(false)
    }
  }

  const getStatusBadge = (result: TestResult | null) => {
    if (!result) return null

    return result.success ? (
      <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
        <CheckCircle className="w-3 h-3 mr-1" />
        Success
      </Badge>
    ) : (
      <Badge variant="destructive">
        <AlertCircle className="w-3 h-3 mr-1" />
        Failed
      </Badge>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Email Configuration Test</h1>
        <p className="text-gray-600">
          Test the email configuration to ensure emails are being sent correctly.
        </p>
      </div>

      <div className="grid gap-6">
        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Email Test Controls
            </CardTitle>
            <CardDescription>
              Run a test to verify email configuration and connectivity.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Button
                  onClick={runEmailTest}
                  disabled={testing || testingOrder}
                  className="flex items-center gap-2"
                >
                  {testing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Testing SMTP...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4" />
                      Test SMTP Connection
                    </>
                  )}
                </Button>
                {getStatusBadge(testResult)}
              </div>

              <div className="flex items-center gap-4">
                <Button
                  onClick={runOrderEmailTest}
                  disabled={testing || testingOrder}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  {testingOrder ? (
                    <>
                      <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
                      Testing Order Emails...
                    </>
                  ) : (
                    <>
                      <Mail className="w-4 h-4" />
                      Test Order Emails
                    </>
                  )}
                </Button>
                {getStatusBadge(orderTestResult)}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* SMTP Test Results */}
        {testResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="w-5 h-5" />
                SMTP Test Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Status</h4>
                  <p className={testResult.success ? 'text-green-600' : 'text-red-600'}>
                    {testResult.message}
                  </p>
                </div>

                {testResult.success && testResult.result && (
                  <div>
                    <h4 className="font-semibold mb-2">Email Details</h4>
                    <div className="bg-gray-50 p-3 rounded-md">
                      <pre className="text-sm overflow-auto">
                        {JSON.stringify(testResult.result, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}

                {!testResult.success && testResult.error && (
                  <div>
                    <h4 className="font-semibold mb-2 text-red-600">Error Details</h4>
                    <div className="bg-red-50 p-3 rounded-md border border-red-200">
                      <pre className="text-sm text-red-700 overflow-auto">
                        {JSON.stringify(testResult.error, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Order Email Test Results */}
        {orderTestResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="w-5 h-5" />
                Order Email Test Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Status</h4>
                  <p className={orderTestResult.success ? 'text-green-600' : 'text-red-600'}>
                    {orderTestResult.message}
                  </p>
                </div>

                {orderTestResult.success && orderTestResult.result && (
                  <div>
                    <h4 className="font-semibold mb-2">Test Results</h4>
                    <div className="bg-gray-50 p-3 rounded-md">
                      <pre className="text-sm overflow-auto">
                        {JSON.stringify(orderTestResult.result, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}

                {!orderTestResult.success && orderTestResult.error && (
                  <div>
                    <h4 className="font-semibold mb-2 text-red-600">Error Details</h4>
                    <div className="bg-red-50 p-3 rounded-md border border-red-200">
                      <pre className="text-sm text-red-700 overflow-auto">
                        {JSON.stringify(orderTestResult.error, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Configuration Info */}
        <Card>
          <CardHeader>
            <CardTitle>Current Email Configuration</CardTitle>
            <CardDescription>
              Environment variables used for email configuration.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>SMTP Host:</strong> {process.env.NEXT_PUBLIC_SMTP_HOST || 'Not set'}
              </div>
              <div>
                <strong>SMTP Port:</strong> {process.env.NEXT_PUBLIC_SMTP_PORT || 'Not set'}
              </div>
              <div>
                <strong>From Email:</strong> {process.env.NEXT_PUBLIC_SMTP_FROM || 'Not set'}
              </div>
              <div>
                <strong>From Name:</strong> {process.env.NEXT_PUBLIC_SMTP_FROM_NAME || 'Not set'}
              </div>
              <div>
                <strong>Company Email:</strong> {process.env.NEXT_PUBLIC_COMPANY_EMAIL || 'Not set'}
              </div>
              <div>
                <strong>Environment:</strong> {process.env.NODE_ENV || 'Not set'}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Troubleshooting</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div>
                <strong>1. Check Environment Variables:</strong>
                <p className="text-gray-600 ml-4">
                  Ensure all SMTP_* variables are set in your .env file.
                </p>
              </div>
              <div>
                <strong>2. Verify SMTP Credentials:</strong>
                <p className="text-gray-600 ml-4">
                  Make sure your SMTP username and password are correct.
                </p>
              </div>
              <div>
                <strong>3. Check Server Logs:</strong>
                <p className="text-gray-600 ml-4">
                  Look at the server console for detailed error messages.
                </p>
              </div>
              <div>
                <strong>4. Test Email Provider:</strong>
                <p className="text-gray-600 ml-4">
                  Try sending an email through your provider&apos;s web interface.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
