import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { generateLevels } from '@/lib/level-generation'



// POST - Generate and apply levels based on configuration
export async function POST(request: NextRequest) {
  try {
    const { configId }: { configId: string } = await request.json()
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get the configuration
    const { data: config, error: configError } = await supabase
      .from('level_management_config')
      .select('*')
      .eq('id', configId)
      .single()

    if (configError || !config) {
      return NextResponse.json({ error: 'Configuration not found' }, { status: 404 })
    }

    // Generate levels
    const generatedLevels = generateLevels(config)

    // Start transaction by deleting existing levels and inserting new ones
    const { error: deleteError } = await supabase
      .from('user_levels')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000') // Delete all

    if (deleteError) {
      console.error('Error deleting existing levels:', deleteError)
      return NextResponse.json({ error: 'Error deleting existing levels' }, { status: 500 })
    }

    // Insert new levels
    const levelsToInsert = generatedLevels.map(level => ({
      level: level.level,
      name: level.name,
      minimum_points: level.minimum_points,
      discount_percentage: level.discount_percentage,
      points_multiplier: level.points_multiplier
    }))

    const { data: insertedLevels, error: insertError } = await supabase
      .from('user_levels')
      .insert(levelsToInsert)
      .select()

    if (insertError) {
      console.error('Error inserting new levels:', insertError)
      return NextResponse.json({ error: 'Fehler beim Erstellen neuer Level' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: `${generatedLevels.length} Level erfolgreich generiert`,
      levels: insertedLevels
    })
  } catch (error) {
    console.error('Error in level generation:', error)
    return NextResponse.json({ error: 'Interner Serverfehler' }, { status: 500 })
  }
}
