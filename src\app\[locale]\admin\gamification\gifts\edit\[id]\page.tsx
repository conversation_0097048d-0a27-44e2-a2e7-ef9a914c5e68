'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import GiftThresholdForm from '@/components/admin/gift-threshold-form'

interface GiftThreshold {
  id: string
  threshold_amount: number
  gift_product_ids: string[]
  is_active: boolean
}

interface EditGiftPageProps {
  params: Promise<{ locale: string; id: string }>
}

export default function EditGiftPage({ params }: EditGiftPageProps) {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = createClient()

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [gift, setGift] = useState<GiftThreshold | null>(null)
  const [giftId, setGiftId] = useState('')

  useEffect(() => {
    const getParams = async () => {
      const resolved = await params
      setGiftId(resolved.id)
    }
    getParams()
  }, [params])

  useEffect(() => {
    const checkAuthAndLoad = async () => {
      if (!giftId) return
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          router.push(`/${locale}/login`)
          return
        }
        const { data: profile } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()
        if (!profile?.is_admin) {
          router.push(`/${locale}`)
          return
        }
        setAuthChecked(true)
        const { data: giftData, error } = await supabase
          .from('gift_thresholds')
          .select('*')
          .eq('id', giftId)
          .single()
        if (error || !giftData) {
          router.push(`/${locale}/admin/gamification`)
          return
        }
        setGift(giftData)
        setLoading(false)
      } catch {
        router.push(`/${locale}`)
      }
    }
    if (!authChecked && giftId) {
      checkAuthAndLoad()
    }
  }, [authChecked, giftId, locale, router, supabase])

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (!gift) {
    return null
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${locale}/admin/gamification`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back')}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{t('gamificationPage.newGift')}</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('gamificationPage.newGift')}</CardTitle>
        </CardHeader>
        <CardContent>
          <GiftThresholdForm locale={locale} gift={gift} />
        </CardContent>
      </Card>
    </div>
  )
}

