"use client";

import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { HelpCircle } from 'lucide-react';

export default function FaqPage() {
  const t = useTranslations('faq');

  const faqs = [
    {
      question: t('questions.coffeeBoxBuilder.question'),
      answer: t('questions.coffeeBoxBuilder.answer')
    },
    {
      question: t('questions.paymentMethods.question'),
      answer: t('questions.paymentMethods.answer')
    },
    {
      question: t('questions.shippingCosts.question'),
      answer: t('questions.shippingCosts.answer')
    },
    {
      question: t('questions.deliveryTime.question'),
      answer: t('questions.deliveryTime.answer')
    },
    {
      question: t('questions.cancelOrder.question'),
      answer: t('questions.cancelOrder.answer')
    },
    {
      question: t('questions.compatibility.question'),
      answer: t('questions.compatibility.answer')
    },
    {
      question: t('questions.shelfLife.question'),
      answer: t('questions.shelfLife.answer')
    },
    {
      question: t('questions.organicCoffee.question'),
      answer: t('questions.organicCoffee.answer')
    },
    {
      question: t('questions.subscription.question'),
      answer: t('questions.subscription.answer')
    },
    {
      question: t('questions.customerService.question'),
      answer: t('questions.customerService.answer')
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
            <HelpCircle className="h-8 w-8 text-blue-600" />
          </div>
          <h1 className="text-4xl font-bold mb-4">{t('title')}</h1>
          <p className="text-xl text-muted-foreground">
            {t('subtitle')}
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{t('sections.frequentlyAsked')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible className="w-full">
              {faqs.map((faq, index) => (
                <AccordionItem key={index} value={`item-${index}`}>
                  <AccordionTrigger className="text-left">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-muted-foreground">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </CardContent>
        </Card>

        <Card className="mt-8">
          <CardHeader>
            <CardTitle>{t('sections.moreQuestions')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              {t('sections.moreQuestionsText')}
            </p>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <p className="font-medium">{t('sections.email')}</p>
                <p className="text-sm text-muted-foreground"><EMAIL></p>
              </div>
              <div>
                <p className="font-medium">{t('sections.phone')}</p>
                <p className="text-sm text-muted-foreground">+41 44 123 45 67</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
