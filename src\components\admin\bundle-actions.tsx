'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { MoreHorizontal, Edit, Trash2, ToggleLeft, ToggleRight } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'

interface Bundle {
  id: string
  title: string
  slug: string
  is_available: boolean
}

interface BundleActionsProps {
  bundle: Bundle
  locale?: string
}

export default function BundleActions({ bundle, locale = 'de' }: BundleActionsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()
  const router = useRouter()
  const t = useTranslations('admin.bundleManagement')

  const handleToggleAvailable = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/bundles/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: bundle.id,
          is_available: !bundle.is_available,
        }),
      })

      if (response.ok) {
        toast({
          title: t('successToggle'),
          description: `${t('successToggle')} ${!bundle.is_available ? t('activated') : t('deactivated')}.`,
        })
        router.refresh()
      } else {
        throw new Error(t('errorToggle'))
      }
    } catch {
      toast({
        title: t('errorToggle'),
        description: t('errorToggle'),
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/bundles/${bundle.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        toast({
          title: t('successDelete'),
          description: t('successDelete'),
        })
        router.refresh()
      } else {
        throw new Error(t('errorDelete'))
      }
    } catch {
      toast({
        title: t('errorDelete'),
        description: t('errorDelete'),
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
      setShowDeleteDialog(false)
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Menü öffnen</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={() => router.push(`/${locale}/admin/bundles/edit/${bundle.id}`)}
          >
            <Edit className="mr-2 h-4 w-4" />
            {t('edit')}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleToggleAvailable} disabled={loading}>
            {bundle.is_available ? (
              <>
                <ToggleLeft className="mr-2 h-4 w-4" />
                {t('deactivate')}
              </>
            ) : (
              <>
                <ToggleRight className="mr-2 h-4 w-4" />
                {t('activate')}
              </>
            )}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => setShowDeleteDialog(true)}
            className="text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {t('delete')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('deleteConfirm')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('deleteMessage')} &quot;{bundle.title}&quot;
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={loading}
            >
              {loading ? t('deleting') : t('delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
